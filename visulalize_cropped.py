import os
import cv2
import numpy as np
import matplotlib.pyplot as plt

# Configuration
CROPPED_DIR = "cropped_pens"
IMAGES_DIR = os.path.join(CROPPED_DIR, "images")
LABELS_DIR = os.path.join(CROPPED_DIR, "labels")

def visualize_cropped_pen(image_name):
    # Build paths
    img_path = os.path.join(IMAGES_DIR, image_name)
    label_path = os.path.join(LABELS_DIR, os.path.splitext(image_name)[0] + ".txt")
    
    # Read image
    img = cv2.imread(img_path)
    if img is None:
        print(f"Error: Image not found at {img_path}")
        return
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)  # Convert to RGB for matplotlib
    height, width = img.shape[:2]
    
    # Read keypoints
    keypoints = []
    if os.path.exists(label_path):
        with open(label_path, 'r') as f:
            for line in f:
                try:
                    x, y = map(float, line.split())
                    keypoints.append((x, y))
                except ValueError:
                    continue
    
    # Create figure
    plt.figure(figsize=(10, 6))
    
    # Draw image
    plt.imshow(img)
    plt.title(f"Pen: {image_name}\nKeypoints: {len(keypoints)}")
    
    # Draw keypoints
    for i, (x, y) in enumerate(keypoints):
        # Convert normalized to pixel coordinates
        px = int(x * width)
        py = int(y * height)
        
        # Draw point and index
        plt.scatter(px, py, s=100, c='red', marker='o', edgecolors='white')
        plt.text(px + 5, py - 5, str(i+1), fontsize=12, color='white', 
                 bbox=dict(facecolor='red', alpha=0.7, boxstyle='round'))
    
    plt.axis('off')
    plt.tight_layout()
    plt.show()

def visualize_all_cropped_pens():
    # Get all cropped pen images
    pen_images = [f for f in os.listdir(IMAGES_DIR) 
                 if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    if not pen_images:
        print("No cropped pen images found in directory:", IMAGES_DIR)
        return
    
    print(f"Found {len(pen_images)} cropped pen images")
    
    # Visualize each pen
    for img_name in pen_images:
        visualize_cropped_pen(img_name)

# Choose one of these options:
# Option 1: Visualize a specific cropped pen
# visualize_cropped_pen("example_image_pen0.jpg")

# Option 2: Visualize all cropped pens sequentially
visualize_all_cropped_pens()