import os
import cv2
import numpy as np

# Configuration
CROPPED_DIR = "cropped_pens"
IMAGES_DIR = os.path.join(CROPPED_DIR, "images")
NEW_LABELS_DIR = os.path.join(CROPPED_DIR, "new_labels")
os.makedirs(NEW_LABELS_DIR, exist_ok=True)

# Global variables to store click position
click_pos = None
current_image = None
image_name = ""

def mouse_callback(event, x, y, flags, param):
    """Mouse callback function to capture click position"""
    global click_pos
    if event == cv2.EVENT_LBUTTONDOWN:
        click_pos = (x, y)
        
        # Draw a marker at clicked position
        cv2.circle(current_image, (x, y), 5, (0, 0, 255), -1)
        cv2.imshow("Annotate Pen Tip", current_image)

def annotate_pen_tip():
    """Annotate pen tip for all cropped images"""
    global current_image, image_name, click_pos
    
    # Get list of all cropped pen images
    image_files = sorted([f for f in os.listdir(IMAGES_DIR) 
                         if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
    
    if not image_files:
        print("No cropped pen images found!")
        return
    
    print(f"Found {len(image_files)} cropped pen images")
    print("Instructions:")
    print("- Click on the pen tip to annotate")
    print("- Press 's' to save and move to next image")
    print("- Press 'r' to reset the annotation")
    print("- Press 'q' to quit")
    
    # Create OpenCV window
    cv2.namedWindow("Annotate Pen Tip")
    cv2.setMouseCallback("Annotate Pen Tip", mouse_callback)
    
    for img_file in image_files:
        # Reset click position
        click_pos = None
        
        # Load image
        img_path = os.path.join(IMAGES_DIR, img_file)
        image = cv2.imread(img_path)
        if image is None:
            print(f"Error loading image: {img_file}")
            continue
            
        # Store as global for callback
        current_image = image.copy()
        image_name = img_file
        
        # Display image
        display_img = current_image.copy()
        cv2.putText(display_img, img_file, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.imshow("Annotate Pen Tip", display_img)
        
        while True:
            key = cv2.waitKey(1) & 0xFF
            
            # Save and move to next image
            if key == ord('s'):
                if click_pos is None:
                    print("Please click on the pen tip before saving!")
                    continue
                    
                # Save annotation
                save_annotation(img_file, click_pos, image.shape)
                print(f"Saved annotation for {img_file}")
                break
                
            # Reset annotation
            elif key == ord('r'):
                click_pos = None
                current_image = image.copy()
                display_img = current_image.copy()
                cv2.putText(display_img, img_file, (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                cv2.imshow("Annotate Pen Tip", display_img)
                print("Annotation reset")
                
            # Quit program
            elif key == ord('q'):
                cv2.destroyAllWindows()
                print("Annotation process aborted")
                return
                
    cv2.destroyAllWindows()
    print("\nAnnotation completed!")

def save_annotation(img_file, click_pos, image_shape):
    """Save normalized coordinates to label file"""
    # Get image dimensions
    height, width = image_shape[:2]
    
    # Normalize coordinates (x, y)
    x_norm = click_pos[0] / width
    y_norm = click_pos[1] / height
    
    # Create output filename
    base_name = os.path.splitext(img_file)[0]
    label_file = os.path.join(NEW_LABELS_DIR, f"{base_name}.txt")
    
    # Save normalized coordinates
    with open(label_file, 'w') as f:
        f.write(f"{x_norm:.6f} {y_norm:.6f}")

if __name__ == "__main__":
    annotate_pen_tip()